# Send Message Page Documentation

## Overview
The Send Message page allows users to send Zalo notifications using predefined templates and an Excel file containing recipient data.

## Features

### 1. Template Selection
- **Template c<PERSON> nhân (Personal Template)**: Dropdown list populated from GraphQL query
- **Template công ty (Company Template)**: Dropdown list populated from GraphQL query
- Both dropdowns fetch data from the same GraphQL endpoint with different filtering

### 2. File Upload
- **Excel File Upload**: Accepts `.xlsx` and `.xls` files
- File validation to ensure only Excel files are accepted
- Visual feedback showing selected file name

### 3. Form Submission
- **Send Message Button**: Submits the form data to the API
- Loading state with spinner during submission
- Success/error feedback to the user

## Technical Implementation

### GraphQL Query
```graphql
query GetZaloNotificationSettings {
  zaloNotificationSettings(
    where: {
      dmsDataType: {
        eq: NONE
      }
    }
  ) {
    items {
      name
      id
      zaloTemplateId
    }
    totalCount
  }
}
```

### API Endpoint
- **URL**: `http://localhost:5000/api/ZaloNotification/SendByExcel`
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Parameters**:
  - `personalTemplateId`: Selected personal template ID
  - `companyTemplateId`: Selected company template ID
  - `excelFile`: Uploaded Excel file

### Form Validation
- All fields are required
- File must be a valid Excel format (.xlsx or .xls)
- Real-time error clearing when user interacts with fields

### UI Components Used
- `Card`: Main container for the form
- `Input`: File upload input
- `Button`: Submit button with loading state
- `Select`: Custom dropdown components for template selection

## File Structure
```
src/
├── app/
│   └── send-message/
│       └── page.tsx          # Main Send Message page component
├── components/
│   └── ui/
│       ├── button.tsx        # Button component
│       ├── select.tsx        # Select/dropdown component
│       ├── card.tsx          # Card component (existing)
│       └── input.tsx         # Input component (existing)
├── graphql/
│   └── queries.ts            # GraphQL queries including notification settings
└── components/
    └── Header.tsx            # Updated navigation with Send Message link
```

## Usage Instructions

1. **Navigate to Send Message**: Click "Send Message" in the navigation menu
2. **Select Templates**: Choose both personal and company templates from the dropdowns
3. **Upload Excel File**: Click the file input and select an Excel file containing recipient data
4. **Submit**: Click "Gửi tin nhắn" (Send Message) to submit the form
5. **Monitor Progress**: Watch for loading state and success/error messages

## Error Handling
- Network errors are caught and displayed to the user
- Form validation errors are shown inline
- File type validation prevents invalid file uploads
- Success feedback confirms successful submission

## Environment Variables
- `NEXT_PUBLIC_API_URL`: Base URL for the API (defaults to http://localhost:5000)
- `NEXT_PUBLIC_GRAPHQL_URL`: GraphQL endpoint URL (defaults to http://localhost:5000/graphql)

## Dependencies
- Apollo Client for GraphQL queries
- React hooks for state management
- Tailwind CSS for styling
- Lucide React for icons
