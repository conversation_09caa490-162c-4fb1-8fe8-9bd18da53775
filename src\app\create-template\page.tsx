"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { FileText, MessageSquare } from "lucide-react"

// Constants
const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"
const ZALO_ACCOUNT_ID = process.env.NEXT_PUBLIC_ZALO_ACCOUNT_ID || 1

interface FormData {
  templateId: string
  templateName: string
  customerName: string
  phoneNumber: string
  contents: { [key: number]: { checked: boolean; text: string } }
}

interface FormErrors {
  templateId?: string
  templateName?: string
  customerName?: string
  phoneNumber?: string
  contents?: { [key: number]: string | undefined }
}

export default function CreateTemplatePage() {
  const [formData, setFormData] = useState<FormData>({
    templateId: "",
    templateName: "",
    customerName: "customer_name",
    phoneNumber: "phone_number",
    contents: Array.from({ length: 10 }, (_, i) => ({ [i + 1]: { checked: false, text: "" } })).reduce((acc, curr) => ({ ...acc, ...curr }), {})
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    console.log('Validating form with data:', formData)

    // Validate required fields
    if (!formData.templateId.trim()) {
      newErrors.templateId = "Vui lòng không bỏ trống."
      console.log('Template ID is empty')
    }
    if (!formData.templateName.trim()) {
      newErrors.templateName = "Vui lòng không bỏ trống."
      console.log('Template Name is empty')
    }
    if (!formData.customerName.trim()) {
      newErrors.customerName = "Vui lòng không bỏ trống."
      console.log('Customer Name is empty')
    }
    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = "Vui lòng không bỏ trống."
      console.log('Phone Number is empty')
    }

    // Validate content fields (only if checkbox is checked)
    const contentErrors: { [key: number]: string } = {}
    for (let i = 1; i <= 10; i++) {
      if (formData.contents[i]?.checked) {
        console.log(`Content ${i} is checked, text: "${formData.contents[i]?.text}"`)
        if (!formData.contents[i]?.text.trim()) {
          contentErrors[i] = "Cần nhập thông tin."
          console.log(`Content ${i} validation failed - empty text`)
        }
      }
    }

    // Only add contents to errors if there are actual content errors
    if (Object.keys(contentErrors).length > 0) {
      newErrors.contents = contentErrors
    }

    setErrors(newErrors)

    // Check if there are any errors (including content errors)
    const hasBasicErrors = Object.keys(newErrors).filter(key => key !== 'contents').length > 0
    const hasContentErrors = Object.keys(contentErrors).length > 0

    console.log('Validation result:', { hasBasicErrors, hasContentErrors, newErrors })

    return !hasBasicErrors && !hasContentErrors
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleContentChange = (index: number, field: 'checked' | 'text', value: boolean | string) => {
    setFormData(prev => ({
      ...prev,
      contents: {
        ...prev.contents,
        [index]: {
          ...prev.contents[index],
          [field]: value
        }
      }
    }))

    // Clear error when user starts typing or unchecks
    if (errors.contents?.[index]) {
      setErrors(prev => ({
        ...prev,
        contents: {
          ...prev.contents,
          [index]: undefined
        }
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    console.log('Form submitted!')
    console.log('Form data:', formData)

    if (!validateForm()) {
      console.log('Form validation failed')
      return
    }

    console.log('Form validation passed')
    setIsSubmitting(true)

    try {
      // Helper function to convert index to DMS field letter (1->A, 2->B, etc.)
      const indexToDmsField = (index: number): string => {
        return String.fromCharCode(65 + index) // 65 is 'A', so 64 + 1 = 'A'
      }

      // Prepare data for API in the required format
      const apiData = {
        name: formData.templateName,
        isEnabled: true,
        zaloTemplateId: formData.templateId,
        dmsDataType: "None",
        zaloAccountId: ZALO_ACCOUNT_ID,
        zaloNotificationDataMappings: [
          // Always include customer_name mapping
          {
            dmsFields: "B",
            dmsFieldTypes: "string",
            zaloField: "customer_name",
            zaloFieldFormat: null
          },
          // Always include phone_number mapping
          {
            dmsFields: "C",
            dmsFieldTypes: "string",
            zaloField: "phone_number",
            zaloFieldFormat: null
          },
          // Add mappings for checked content fields
          ...Object.entries(formData.contents)
            .filter(([, content]) => content.checked && content.text.trim())
            .map(([index, content]) => ({
              dmsFields: indexToDmsField(parseInt(index) + 3), // +2 because B,C are taken by customer_name, phone_number
              dmsFieldTypes: "string",
              zaloField: content.text.trim(),
              zaloFieldFormat: null
            }))
        ]
      }

      console.log('API Data to send:', JSON.stringify(apiData, null, 2))
      console.log('API URL:', `${API_URL}/api/ZaloSetting/AddNotificationSetting`)

      // Temporary: Test with mock response (comment out for real API)
      // const response = { ok: true, status: 200, json: async () => ({ success: true }) }

      const response = await fetch(`${API_URL}/api/ZaloSetting/AddNotificationSetting`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      })

      console.log('Response status:', response.status)
      console.log('Response ok:', response.ok)

      if (response.ok) {
        console.log('API call successful!')
        const responseData = await response.json()
        console.log('Response data:', responseData)
        alert('Template đã được tạo thành công!')
        // Reset form
        setFormData({
          templateId: "",
          templateName: "",
          customerName: "customer_name",
          phoneNumber: "phone_number",
          contents: Array.from({ length: 10 }, (_, i) => ({ [i + 1]: { checked: false, text: "" } })).reduce((acc, curr) => ({ ...acc, ...curr }), {})
        })
      } else {
        console.log('API call failed with status:', response.status)
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))
        console.log('Error data:', errorData)
        alert(`Lỗi: ${errorData.message || 'Không thể tạo template'}`)
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('Có lỗi xảy ra khi tạo template')
    } finally {
      console.log('Setting isSubmitting to false')
      setIsSubmitting(false)
    }
  }

  return (
    <div className="p-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Tạo mẫu template</h1>
        <p className="text-muted-foreground">Tạo mẫu template cho thông báo Zalo</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Thông tin cơ bản</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Template ID */}
            <div>
              <label htmlFor="templateId" className="block text-sm font-medium mb-2">
                Mã Zalo Template Id <span className="text-red-500">*</span>
              </label>
              <Input
                id="templateId"
                type="text"
                value={formData.templateId}
                onChange={(e) => handleInputChange('templateId', e.target.value)}
                className={`w-full ${errors.templateId ? 'border-red-500' : ''}`}
                placeholder="Nhập mã Zalo template"
              />
              {errors.templateId && (
                <p className="text-red-500 text-sm mt-1">{errors.templateId}</p>
              )}
            </div>

            {/* Template Name */}
            <div>
              <label htmlFor="templateName" className="block text-sm font-medium mb-2">
                Tên mẫu Template <span className="text-red-500">*</span>
              </label>
              <Input
                id="templateName"
                type="text"
                value={formData.templateName}
                onChange={(e) => handleInputChange('templateName', e.target.value)}
                className={`w-full ${errors.templateName ? 'border-red-500' : ''}`}
                placeholder="Nhập tên mẫu template"
              />
              {errors.templateName && (
                <p className="text-red-500 text-sm mt-1">{errors.templateName}</p>
              )}
            </div>

            {/* Customer Name */}
            <div>
              <label htmlFor="customerName" className="block text-sm font-medium mb-2">
                Tên khách hàng <span className="text-red-500">*</span>
              </label>
              <Input
                id="customerName"
                type="text"
                value={formData.customerName}
                onChange={(e) => handleInputChange('customerName', e.target.value)}
                className={`w-full ${errors.customerName ? 'border-red-500' : ''}`}
                placeholder="<customer_name>"
              />
              {errors.customerName && (
                <p className="text-red-500 text-sm mt-1">{errors.customerName}</p>
              )}
            </div>

            {/* Phone Number */}
            <div>
              <label htmlFor="phoneNumber" className="block text-sm font-medium mb-2">
                Số điện thoại <span className="text-red-500">*</span>
              </label>
              <Input
                id="phoneNumber"
                type="tel"
                value={formData.phoneNumber}
                onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                className={`w-full ${errors.phoneNumber ? 'border-red-500' : ''}`}
                placeholder="<phone_number>"
              />
              {errors.phoneNumber && (
                <p className="text-red-500 text-sm mt-1">{errors.phoneNumber}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Content Sections */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5" />
              <span>Nội dung template</span>
            </CardTitle>
            <CardDescription>
              <span>Thông tin các Nội dung động tương ứng với các cột trong file excel template.</span>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 space-y-4 bg-gray-50/50">
              {Array.from({ length: 10 }, (_, i) => i + 1).map((index) => {
                // Convert index to column letter (1->C, 2->D, etc. since A,B are reserved)
                const columnLetter = String.fromCharCode(65 + index + 3) // +2 because B,C are reserved

                return (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id={`content-${index}`}
                        checked={formData.contents[index]?.checked || false}
                        onChange={(e) => handleContentChange(index, 'checked', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor={`content-${index}`} className="text-sm font-medium text-gray-900">
                        Cột {columnLetter}
                      </label>
                    </div>
                  {formData.contents[index]?.checked && (
                    <div className="ml-7">
                      <Input
                        type="text"
                        value={formData.contents[index]?.text || ''}
                        onChange={(e) => handleContentChange(index, 'text', e.target.value)}
                        className={`w-full ${errors.contents?.[index] ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'}`}
                        placeholder={`Nhập thông tin cho Cột ${columnLetter}`}
                      />
                      {errors.contents?.[index] && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.contents[index]}
                        </p>
                      )}
                    </div>
                  )}
                </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
          >
            {isSubmitting ? 'Đang tạo...' : 'Tạo template'}
          </button>
        </div>
      </form>
    </div>
  )
}
