"use client"

import { useState } from "react"
import { useQuery } from "@apollo/client/react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { MessageSquare, Upload, Send } from "lucide-react"
import { GET_ZALO_NOTIFICATION_SETTINGS } from "@/graphql/queries"
import exampleExcelFile from "@/assets/TemplateZaloZns.xlsx"
// Constants
const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000"

interface ZaloNotificationSetting {
  id: string
  name: string
  zaloTemplateId: string
}

interface ZaloNotificationSettingsResponse {
  zaloNotificationSettings: {
    items: ZaloNotificationSetting[]
    totalCount: number
  }
}

interface FormData {
  personalTemplate: string
  companyTemplate: string
  excelFile: File | null
}

interface FormErrors {
  personalTemplate?: string
  companyTemplate?: string
  excelFile?: string
  submit?: string
}

export default function SendMessagePage() {
  const [formData, setFormData] = useState<FormData>({
    personalTemplate: "0",
    companyTemplate: "0",
    excelFile: null,
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)

  // Fetch notification settings
  const { loading, error, data } = useQuery<ZaloNotificationSettingsResponse>(
    GET_ZALO_NOTIFICATION_SETTINGS
  )

  const templates = data?.zaloNotificationSettings?.items || []

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null
    setFormData(prev => ({ ...prev, excelFile: file }))
    // Clear error when user selects a file
    if (errors.excelFile) {
      setErrors(prev => ({ ...prev, excelFile: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Check if at least one template is selected (not default "0")
    if (formData.personalTemplate === "0" && formData.companyTemplate === "0") {
      newErrors.personalTemplate = "Vui lòng chọn ít nhất 1 template để gửi tin nhắn"
      newErrors.companyTemplate = "Vui lòng chọn ít nhất 1 template để gửi tin nhắn"
    }

    if (!formData.excelFile) {
      newErrors.excelFile = "Vui lòng chọn file Excel"
    } else if (!formData.excelFile.name.match(/\.(xlsx|xls)$/i)) {
      newErrors.excelFile = "Vui lòng chọn file Excel hợp lệ (.xlsx hoặc .xls)"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setErrors({})

    try {
      const formDataToSend = new FormData()

      // Only append template IDs if they are selected (not "0")
      if (formData.personalTemplate !== "0") {
        formDataToSend.append("personalTemplateId", formData.personalTemplate)
      }
      if (formData.companyTemplate !== "0") {
        formDataToSend.append("companyTemplateId", formData.companyTemplate)
      }

      if (formData.excelFile) {
        formDataToSend.append("excelFile", formData.excelFile)
      }

      const response = await fetch(`${API_URL}/api/ZaloNotification/SendByExcel`, {
        method: "POST",
        body: formDataToSend,
      })

      if (!response.ok) {
        const errorData = await response.text()
        throw new Error(errorData || "Có lỗi xảy ra khi gửi tin nhắn")
      }

      setSubmitSuccess(true)
      // Reset form
      setFormData({
        personalTemplate: "0",
        companyTemplate: "0",
        excelFile: null,
      })
      
      // Reset file input
      const fileInput = document.getElementById("excel-file") as HTMLInputElement
      if (fileInput) {
        fileInput.value = ""
      }

    } catch (error) {
      console.error("Error sending message:", error)
      setErrors({
        submit: error instanceof Error ? error.message : "Có lỗi xảy ra khi gửi tin nhắn"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="p-8">
        <div className="text-center">Đang tải...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="text-center text-red-500">
          Lỗi khi tải dữ liệu: {error.message}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col items-center p-8">
      <div className="w-full max-w-2xl space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight">Gửi tin nhắn</h1>
          <p className="text-muted-foreground">
            Gửi tin nhắn Zalo theo template và danh sách từ file Excel
          </p>
        </div>

        <Card className="w-full">
        <CardHeader className="flex flex-row items-center space-y-0 pb-2">
          <div className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Thông tin gửi tin nhắn Zalo dùng excel file</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          {submitSuccess && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-800">Gửi tin nhắn thành công!</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Personal Template Dropdown */}
            <div>
              <label htmlFor="personal-template" className="block text-sm font-medium mb-2">
                Template cá nhân <span className="text-red-500">*</span>
              </label>
              <select
                id="personal-template"
                value={formData.personalTemplate}
                onChange={(e) => handleInputChange("personalTemplate", e.target.value)}
                className="flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="0">Chọn template cá nhân</option>
                {templates.map((template) => (
                  <option key={template.id} value={template.id}>
                    {template.name}
                  </option>
                ))}
              </select>
              {errors.personalTemplate && (
                <p className="text-sm text-red-500 mt-1">{errors.personalTemplate}</p>
              )}
            </div>

            {/* Company Template Dropdown */}
            <div>
              <label htmlFor="company-template" className="block text-sm font-medium mb-2">
                Template công ty <span className="text-red-500">*</span>
              </label>
              <select
                id="company-template"
                value={formData.companyTemplate}
                onChange={(e) => handleInputChange("companyTemplate", e.target.value)}
                className="flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="0">Chọn template công ty</option>
                {templates.map((template) => (
                  <option key={template.id} value={template.id}>
                    {template.name}
                  </option>
                ))}
              </select>
              {errors.companyTemplate && (
                <p className="text-sm text-red-500 mt-1">{errors.companyTemplate}</p>
              )}
            </div>

            {/* File Upload */}
            <div>
              <label htmlFor="excel-file" className="block text-sm font-medium mb-2">
                File Excel <span className="text-red-500">*</span>
              </label>
              <div className="flex items-center space-x-2">
                <Input
                  id="excel-file"
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleFileChange}
                  className="flex-1"
                />
                <Upload className="h-4 w-4 text-muted-foreground" />
              </div>
              {formData.excelFile && (
                <p className="text-sm text-muted-foreground mt-1">
                  Đã chọn: {formData.excelFile.name}
                </p>
              )}
              {errors.excelFile && (
                <p className="text-sm text-red-500 mt-1">{errors.excelFile}</p>
              )}
              <div className="mt-2">
                <a
                  href={exampleExcelFile}
                  download="TemplateZaloZns.xlsx"
                  className="text-sm text-primary hover:text-primary/80 underline transition-colors"
                >
                  Tải file mẫu
                </a>
              </div>
            </div>

            {/* Submit Error */}
            {errors.submit && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-800">{errors.submit}</p>
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Đang gửi...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Gửi tin nhắn
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}
