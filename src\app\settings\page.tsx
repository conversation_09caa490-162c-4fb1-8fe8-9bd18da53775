import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Settings, Database, Bell, Shield, Palette } from "lucide-react"

export default function SettingsPage() {
  return (
    <div className="p-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">Manage your application preferences and configuration</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Database Settings */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">Database Configuration</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="graphql-url" className="block text-sm font-medium mb-2">
                GraphQL Endpoint
              </label>
              <Input
                id="graphql-url"
                type="url"
                placeholder="http://localhost:5000/graphql"
                className="w-full"
              />
            </div>
            <div>
              <label htmlFor="timeout" className="block text-sm font-medium mb-2">
                Request Timeout (ms)
              </label>
              <Input
                id="timeout"
                type="number"
                placeholder="30000"
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">Notifications</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <label htmlFor="email-notifications" className="text-sm font-medium">
                Email Notifications
              </label>
              <input
                id="email-notifications"
                type="checkbox"
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
            </div>
            <div className="flex items-center justify-between">
              <label htmlFor="push-notifications" className="text-sm font-medium">
                Push Notifications
              </label>
              <input
                id="push-notifications"
                type="checkbox"
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
            </div>
            <div>
              <label htmlFor="notification-email" className="block text-sm font-medium mb-2">
                Notification Email
              </label>
              <Input
                id="notification-email"
                type="email"
                placeholder="<EMAIL>"
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">Security</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="api-key" className="block text-sm font-medium mb-2">
                API Key
              </label>
              <Input
                id="api-key"
                type="password"
                placeholder="••••••••••••••••"
                className="w-full"
              />
            </div>
            <div className="flex items-center justify-between">
              <label htmlFor="two-factor" className="text-sm font-medium">
                Two-Factor Authentication
              </label>
              <input
                id="two-factor"
                type="checkbox"
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
            </div>
          </CardContent>
        </Card>

        {/* Display Settings */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="flex items-center space-x-2">
              <Palette className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">Display</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="theme" className="block text-sm font-medium mb-2">
                Theme
              </label>
              <select
                id="theme"
                className="w-full h-9 px-3 py-1 text-sm border border-input bg-background rounded-md focus:outline-none focus:ring-1 focus:ring-ring"
              >
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="system">System</option>
              </select>
            </div>
            <div>
              <label htmlFor="refresh-interval" className="block text-sm font-medium mb-2">
                Auto Refresh Interval (seconds)
              </label>
              <Input
                id="refresh-interval"
                type="number"
                placeholder="30"
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors">
          Save Settings
        </button>
      </div>
    </div>
  )
}
