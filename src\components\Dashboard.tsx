"use client"

import { useState, useEffect } from "react"
import { useQ<PERSON>y, useLazyQuery } from "@apollo/client/react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { PieChart, Pie, Cell, Tooltip, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Legend } from "recharts"
import { CheckCircle2, XCircle } from "lucide-react"
import { GET_ZALO_NOTIFICATIONS } from "@/graphql/queries"

interface ZaloNotification {
  name: string
  status: string
  error?: string
  data?: string
  type: string
  createdTime?: string
}

interface ZaloNotificationsResponse {
  totalCount: number
  items: ZaloNotification[]
}

interface GetZaloNotificationsData {
  zaloNotifications: ZaloNotificationsResponse
}

const COLORS = {
  success: "#10b981",
  failed: "#ef4444",
}

export default function Dashboard() {
  const today = new Date().toISOString().split('T')[0]
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

  const [fromDate, setFromDate] = useState(thirtyDaysAgo)
  const [toDate, setToDate] = useState(today)
  const [allNotifications, setAllNotifications] = useState<ZaloNotification[]>([])
  const [totalCount, setTotalCount] = useState(0)
  const [isFetchingAll, setIsFetchingAll] = useState(false)

  const { loading, error, data, refetch } = useQuery<GetZaloNotificationsData>(GET_ZALO_NOTIFICATIONS, {
    variables: {
      fromDate: `${fromDate}T00:00:00Z`,
      toDate: `${toDate}T23:59:59Z`,
      skip: 0
    },
  })

  const [fetchMore] = useLazyQuery<GetZaloNotificationsData>(GET_ZALO_NOTIFICATIONS)

  // Fetch all pages when totalCount > 900
  useEffect(() => {
    const fetchAllPages = async () => {
      if (!data?.zaloNotifications) return

      const firstPageItems = data.zaloNotifications.items
      const total = data.zaloNotifications.totalCount

      setTotalCount(total)

      if (total <= 900) {
        setAllNotifications(firstPageItems)
        return
      }

      // Need to fetch more pages
      setIsFetchingAll(true)
      const allItems = [...firstPageItems]
      let skip = 900

      try {
        while (skip < total) {
          const result = await fetchMore({
            variables: {
              fromDate: `${fromDate}T00:00:00Z`,
              toDate: `${toDate}T23:59:59Z`,
              skip
            }
          })

          if (result.data?.zaloNotifications?.items) {
            allItems.push(...result.data.zaloNotifications.items)
          }

          skip += 900
        }

        setAllNotifications(allItems)
      } catch (err) {
        console.error("Error fetching all pages:", err)
        setAllNotifications(firstPageItems)
      } finally {
        setIsFetchingAll(false)
      }
    }

    fetchAllPages()
  }, [data, fromDate, toDate, fetchMore])

  const notifications = allNotifications

  const totalSuccess = notifications.filter((n: ZaloNotification) => n.status === "SUCCESS").length
  const totalFailed = notifications.filter((n: ZaloNotification) => n.status === "ERROR").length
  const total = totalCount
  const successRate = total > 0 ? ((totalSuccess / total) * 100).toFixed(1) : "0.0"

  const pieData = [
    { name: "Success", value: totalSuccess },
    { name: "Failed", value: totalFailed },
  ]

  // Group notifications by date and type
  interface DateTypeCount {
    date: string
    APPOINTMENT?: number
    CUSTOMER_BIRTHDAY?: number
    MAINTENANCE_REMINDER?: number
    PAID_REPAIR_ORDER?: number
    WARRANTY_ACTIVATION?: number
    [key: string]: string | number | undefined
  }

  const dateTypeData = notifications.reduce((acc: Record<string, DateTypeCount>, notification: ZaloNotification) => {
    const date = notification.createdTime ? new Date(notification.createdTime).toISOString().split('T')[0] : 'Unknown'
    const type = notification.type || "Unknown"

    if (!acc[date]) {
      acc[date] = { date }
    }

    acc[date][type] = (acc[date][type] as number || 0) + 1

    return acc
  }, {})

  const typeChartData = Object.values(dateTypeData).sort((a, b) => a.date.localeCompare(b.date))

  // Group ERROR notifications by error message/category
  const errorNotifications = notifications.filter((n: ZaloNotification) => n.status === "ERROR")
  const errorCategories = errorNotifications.reduce((acc: Record<string, number>, notification: ZaloNotification) => {
    const errorMsg = notification.error || "Unknown Error"
    // Extract first part of error message as category
    const category = errorMsg.split(':')[0].trim() || errorMsg.substring(0, 50)
    acc[category] = (acc[category] || 0) + 1
    return acc
  }, {})

  const errorChartData = Object.entries(errorCategories)
    .map(([category, count]) => ({ category, count }))
    .sort((a, b) => b.count - a.count)

  const handleApplyFilter = () => {
    setAllNotifications([])
    setTotalCount(0)
    refetch({
      fromDate: `${fromDate}T00:00:00Z`,
      toDate: `${toDate}T23:59:59Z`,
      skip: 0
    })
  }

  if (loading || isFetchingAll) {
    return (
      <div className="p-8 space-y-8">
        <div className="text-center">
          {isFetchingAll ? `Loading all pages... (${notifications.length} / ${totalCount} records)` : "Loading..."}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-8 space-y-8">
        <div className="text-center text-red-600">Error: {error.message}</div>
      </div>
    )
  }

  return (
    <div className="p-8 space-y-8">

      {/* Date Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4 items-end">
            <div className="flex-1 min-w-[200px]">
              <label htmlFor="fromDate" className="block text-sm font-medium mb-2">
                From Date
              </label>
              <div className="relative">
                <Input
                  id="fromDate"
                  type="date"
                  value={fromDate}
                  onChange={(e) => setFromDate(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
            <div className="flex-1 min-w-[200px]">
              <label htmlFor="toDate" className="block text-sm font-medium mb-2">
                To Date
              </label>
              <div className="relative">
                <Input
                  id="toDate"
                  type="date"
                  value={toDate}
                  onChange={(e) => setToDate(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
            <button
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors h-9"
              onClick={handleApplyFilter}
            >
              Apply Filter
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Success</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSuccess}</div>
            <p className="text-xs text-muted-foreground">
              {successRate}% success rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Failed</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalFailed}</div>
            <p className="text-xs text-muted-foreground">
              {((totalFailed / total) * 100).toFixed(1)}% failure rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Operations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{total}</div>
            <p className="text-xs text-muted-foreground">
              All time operations
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Type Comparison Chart */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Notification Types by Date</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={typeChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="APPOINTMENT" fill="#8b5cf6" name="Appointment" />
                <Bar dataKey="CUSTOMER_BIRTHDAY" fill="#3b82f6" name="Customer Birthday" />
                <Bar dataKey="MAINTENANCE_REMINDER" fill="#10b981" name="Maintenance Reminder" />
                <Bar dataKey="PAID_REPAIR_ORDER" fill="#f59e0b" name="Paid Repair Order" />
                <Bar dataKey="WARRANTY_ACTIVATION" fill="#ef4444" name="Warranty Activation" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Error Categories Chart */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Error Categories (Status = ERROR)</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={errorChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" angle={-45} textAnchor="end" height={100} />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#ef4444" name="Error Count" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Success vs Failed Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={(props) => {
                    const { name, percent } = props as unknown as { name: string; percent: number }
                    return `${name}: ${(percent * 100).toFixed(0)}%`
                  }}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((_, index: number) => (
                    <Cell key={`cell-${index}`} fill={index === 0 ? COLORS.success : COLORS.failed} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-[300px] overflow-y-auto">
              {notifications.slice(0, 10).map((notification: ZaloNotification, index: number) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex-1">
                    <div className="text-sm font-medium">{notification.name}</div>
                    <div className="text-xs text-muted-foreground">{notification.type}</div>
                  </div>
                  <div>
                    {notification.status === "success" ? (
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
